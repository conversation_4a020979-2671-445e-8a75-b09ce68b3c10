#!/usr/bin/env python3
"""
Quick Manufacturing Test - Single Test Case

A simplified version to test one manufacturing case quickly and verify the system works.
Run with: docker exec ml-models-app python test_manufacturing_quick.py
"""

import sys
sys.path.append('/home/<USER>/app')

__import__('pysqlite3')
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')

import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_single_manufacturing_case():
    """Test a single manufacturing case to verify the system works"""

    print("🔧 Testing Single Manufacturing Case: Aluminum Die Cast")
    print("=" * 60)

    try:
        # Import the functions we need
        from emissions_factor_matching.predictions import (
            predict_enhanced_input_category,
            spot_modifiers,
            map_isic_classification,
            augment_query_text,
            construct_dynamic_filters,
            re_rank_candidates
        )
        from emissions_factor_matching.dataset import search_candidates_with_fallback
        from emissions_factor_matching.api import get_activity_from_dataset
        from pydantic import BaseModel

        # Create a request model that matches the expected interface
        class TestRequest(BaseModel):
            user_query_primary: str
            user_query_secondary: str | None = None
            lca_lifecycle_stage: str | None = None
            iso_code: str | None = None
            product_category_context: str | None = None
            carbon_only: bool = True

        # Test case: Aluminum Die Cast
        test_query = "aluminum die cast manufacturing process"
        current_ef = "aluminum ingot, primary, to aluminum, cast alloy slab (customized for GLO)"
        ideal_ef = "aluminum production, primary, cast alloy slab from continuous casting (RoW)"

        print(f"Query: {test_query}")
        print(f"Current EF: {current_ef}")
        print(f"Ideal EF: {ideal_ef}")
        print()

        request = TestRequest(user_query_primary=test_query)

        # Run the 9-phase pipeline step by step
        print("🚀 Running 9-Phase AI Pipeline:")
        print("-" * 40)

        # Phase 1.1: Enhanced Input Category Prediction
        print("Phase 1.1: Enhanced Input Category Prediction")
        enhanced_category = predict_enhanced_input_category(request)
        print(f"✅ Enhanced category: {enhanced_category}")
        print()

        # Phase 1.2: Modifier Spotting
        print("Phase 1.2: Modifier Spotting")
        modifiers = spot_modifiers(request, enhanced_category)
        print(f"✅ Modifiers: {modifiers}")
        print()

        # Phase 1.3: ISIC Classification Mapping
        print("Phase 1.3: ISIC Classification Mapping")
        isic_codes = map_isic_classification(enhanced_category, modifiers, request.user_query_primary)
        print(f"✅ ISIC codes: {isic_codes}")
        print()

        # Phase 1.4: Query Text Augmentation
        print("Phase 1.4: Query Text Augmentation")
        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
        print(f"✅ Augmented query: '{augmented_query}'")
        print()

        # Phase 1.5: Dynamic Filter Construction
        print("Phase 1.5: Dynamic Filter Construction")
        dynamic_filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
        print(f"✅ Dynamic filters: {dynamic_filters}")
        print()

        # Phase 1.6: ChromaDB Vector Search
        print("Phase 1.6: ChromaDB Vector Search")
        candidates = search_candidates_with_fallback(
            augmented_query=augmented_query,
            filters=dynamic_filters,
            n_results=10  # Smaller number for quick test
        )
        print(f"✅ Found {len(candidates)} candidates")

        if candidates:
            print("Top 3 candidates:")
            for i, candidate in enumerate(candidates[:3], 1):
                print(f"  {i}. {candidate.activity_name} (Source: {candidate.source})")
        print()

        if not candidates:
            print("❌ No candidates found - stopping test")
            return False

        # Phase 1.7: LLM Re-ranking & Justification
        print("Phase 1.7: LLM Re-ranking & Justification")
        matched_ef = re_rank_candidates(
            request_model=request,
            candidates=candidates,
            augmented_query=augmented_query,
            enhanced_category=enhanced_category,
            modifiers=modifiers,
            isic_codes=isic_codes
        )
        print(f"✅ Selected: '{matched_ef.activity_name}'")
        print(f"✅ Confidence: {matched_ef.confidence} ({matched_ef.confidence_score:.3f})")
        print(f"✅ Source: {matched_ef.source}")
        print()

        # Phase 1.8: Geography Matching & Record Retrieval
        print("Phase 1.8: Geography Matching & Record Retrieval")
        matched_activity = get_activity_from_dataset(
            activity_name=matched_ef.activity_name,
            iso_code="GLO",
            similarity=matched_ef.confidence_score or 0.0,
            reference_product_name=matched_ef.reference_product_name,
            preferred_source=matched_ef.source
        )
        print(f"✅ Final activity: {matched_activity.activity_name}")
        print(f"✅ Final source: {matched_activity.source}")
        print(f"✅ Geography: {matched_activity.geography}")
        print()

        # Analysis
        print("📊 ANALYSIS:")
        print("-" * 40)

        # Check if we got DEFRA source (expected for carbon_only=True)
        if matched_activity.source == "DEFRA":
            print("✅ Source filtering working: Got DEFRA source for carbon_only=True")
        else:
            print(f"⚠️  Source filtering: Got {matched_activity.source} instead of DEFRA")

        # Check for process specificity
        activity_lower = matched_activity.activity_name.lower()
        if any(term in activity_lower for term in ["cast", "casting", "aluminum", "alloy"]):
            print("✅ Process specificity: Activity contains relevant aluminum/casting terms")
        else:
            print("⚠️  Process specificity: Activity may not be specific enough")

        # Simple keyword comparison with ideal
        ideal_keywords = set(ideal_ef.lower().split())
        activity_keywords = set(matched_activity.activity_name.lower().split())
        overlap = len(ideal_keywords & activity_keywords)

        print(f"📈 Keyword overlap with ideal EF: {overlap} common terms")

        if overlap >= 3:
            print("✅ Good alignment with ideal EF")
        elif overlap >= 1:
            print("⚠️  Moderate alignment with ideal EF")
        else:
            print("❌ Poor alignment with ideal EF")

        print()
        print("🎯 SUMMARY:")
        print(f"New System Result: {matched_activity.activity_name}")
        print(f"Confidence: {matched_ef.confidence}")
        print(f"Source: {matched_activity.source}")
        print(f"Explanation: {matched_ef.explanation[:100]}...")

        return True

    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution function"""
    print("🧪 Quick Manufacturing Test - Single Case Validation")
    print("Testing the 9-phase AI system with one aluminum die cast case")
    print()

    success = test_single_manufacturing_case()

    if success:
        print("\n✅ Quick test completed successfully!")
        print("✅ The 9-phase AI system appears to be working")
        print("✅ Ready to run full manufacturing validation")
    else:
        print("\n❌ Quick test failed")
        print("❌ Check the error messages above")
        print("❌ Fix issues before running full validation")

if __name__ == "__main__":
    main()
