# Local Development Setup Guide

This guide will help you transition from Docker to local development while preserving your cached files.

## Prerequisites

- Python 3.10 (not newer)
- Docker Desktop (for Redis)
- Git

## Setup Steps

1. **Install Python 3.10**
   ```bash
   # On macOS
   brew install python@3.10
   ```

2. **Install Docker Desktop**
   - Download and install from: https://www.docker.com/products/docker-desktop
   - Make sure Docker Desktop is running before proceeding

3. **Clone the Repository (if you haven't already)**
   ```bash
   <NAME_EMAIL>:CarbonBright/ml_models.git
   cd ml_models
   ```

4. **Run the Setup Script**
   ```bash
   chmod +x setup_local.sh
   ./setup_local.sh
   ```
   This will:
   - Create a Python virtual environment
   - Install dependencies
   - Set up Redis using Docker
   - Create necessary cache directories
   - Create a `.env` file

5. **Configure Environment Variables**
   - Edit the `.env` file created by the setup script
   - Add your API keys and tokens
   - The cache directories are already configured to match your Docker setup

6. **Activate Virtual Environment**
   ```bash
   source venv/bin/activate
   ```

7. **Start the Application**
   ```bash
   python main.py
   ```

## Cache Directories

The following cache directories are preserved from your Docker setup:
- `./nltk_data`: NLTK data
- `./hf_cache`: Hugging Face cache
- `./chroma_data`: ChromaDB data
- `./chroma_data_eol`: ChromaDB EOL data
- `./disk_cache`: Disk cache
- `./torch_cache`: PyTorch cache

## Troubleshooting

1. **Redis Connection Issues**
   - Ensure Docker Desktop is running
   - Check Redis container: `docker ps | grep ml-model-redis`
   - Check Redis logs: `docker logs ml-model-redis`
   - Restart Redis: `docker restart ml-model-redis`

2. **Cache Issues**
   - Verify cache directories exist and have correct permissions
   - Check `.env` file has correct paths

3. **Python Version Issues**
   - Ensure you're using Python 3.10: `python --version`
   - If using wrong version, activate correct Python: `source venv/bin/activate`

4. **Dependencies Issues**
   - If you see missing packages, run: `pip install -r dependencies/macos/requirements.txt`

## Development Workflow

1. Always activate the virtual environment before working:
   ```bash
   source venv/bin/activate
   ```

2. To install new dependencies:
   ```bash
   pip install <package>
   pip freeze > dependencies/macos/requirements.txt
   ```

3. To run the application:
   ```bash
   python main.py
   ```

## Notes

- Your existing cache files will be preserved and used by the local setup
- The application will run on port 5001 by default
- Redis runs in Docker on port 6379
- All environment variables from your Docker setup are preserved in the `.env` file
- The Redis configuration is loaded from `scripts/config/redis.conf` 