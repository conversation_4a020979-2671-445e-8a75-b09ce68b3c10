#!/usr/bin/env python3
"""
Test script for real-world queries that should have ISIC codes in our database
"""
import sys
sys.path.append('/home/<USER>/app')

from emissions_factor_matching.predictions import map_isic_classification
from emissions_factor_matching.dataset import efs_with_geographies
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_available_codes():
    """Check what ISIC codes are actually available in our database"""
    print("=== AVAILABLE ISIC CODES IN DATABASE ===\n")
    
    # Get all unique ISIC codes
    raw_codes = efs_with_geographies['ISIC Classification'].dropna().unique()
    
    # Parse codes (handle both "4923:Description" and "4923" formats)
    available_codes = set()
    for code in raw_codes:
        if isinstance(code, str) and ':' in code:
            numeric_code = code.split(':')[0].strip()
            available_codes.add(numeric_code)
        elif isinstance(code, (int, str)):
            available_codes.add(str(code))
    
    # Sort and display
    sorted_codes = sorted(available_codes)
    print(f"Total unique ISIC codes: {len(sorted_codes)}")
    print("Available codes:")
    for i, code in enumerate(sorted_codes):
        if i % 10 == 0:  # New line every 10 codes
            print()
        print(f"{code:>4}", end="  ")
    print("\n")
    
    return available_codes

def test_real_world_queries():
    print("=== TESTING REAL-WORLD QUERIES ===\n")
    
    # Get available codes first
    available_codes = check_available_codes()
    
    # Test with queries that should map to codes we actually have
    real_world_tests = [
        {
            "name": "Basic chemicals production",
            "enhanced_category": "CHEMICAL_ORGANIC_SOLVENT",
            "modifiers": ["industrial", "organic"],
            "user_query": "basic chemical manufacturing",
            "likely_codes": ["2011"]  # Basic chemicals - very common
        },
        {
            "name": "Freight transportation",
            "enhanced_category": "SERVICE_TRANSPORT_ROAD_FREIGHT", 
            "modifiers": ["truck", "freight"],
            "user_query": "truck freight transport",
            "likely_codes": ["4923"]  # Road freight - very common
        },
        {
            "name": "Electricity production",
            "enhanced_category": "SERVICE_ENERGY_ELECTRICITY",
            "modifiers": ["power", "generation"],
            "user_query": "electric power generation",
            "likely_codes": ["3510"]  # Electricity generation - very common
        },
        {
            "name": "Manufacturing activity",
            "enhanced_category": "PRODUCT_CONSTRUCTION_MATERIAL",
            "modifiers": ["cement", "concrete"],
            "user_query": "cement manufacturing",
            "likely_codes": ["2394", "2395"]  # Cement/concrete manufacturing
        },
        {
            "name": "Petroleum refining",
            "enhanced_category": "CHEMICAL_FUEL_ENERGY",
            "modifiers": ["petroleum", "refining"],
            "user_query": "petroleum refining",
            "likely_codes": ["1920"]  # Refined petroleum products
        }
    ]
    
    for i, test_case in enumerate(real_world_tests, 1):
        print(f"{i}. Testing: {test_case['name']}")
        print(f"   Category: {test_case['enhanced_category']}")
        print(f"   Modifiers: {test_case['modifiers']}")
        print(f"   User Query: {test_case['user_query']}")
        print(f"   Expected codes in DB: {[c for c in test_case['likely_codes'] if c in available_codes]}")
        
        try:
            result = map_isic_classification(
                test_case['enhanced_category'], 
                test_case['modifiers'],
                test_case['user_query']
            )
            print(f"   LLM Result: {result}")
            
            # Check if any expected codes were found
            found_expected = any(code in result for code in test_case['likely_codes'])
            if found_expected:
                print(f"   ✅ SUCCESS: Found expected code(s)!")
            elif result:
                print(f"   ⚠️  PARTIAL: Got different valid codes: {result}")
            else:
                print(f"   ❌ FAILED: No codes returned")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
        
        print()

if __name__ == "__main__":
    test_real_world_queries()
