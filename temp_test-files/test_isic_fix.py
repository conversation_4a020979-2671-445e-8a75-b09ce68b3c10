#!/usr/bin/env python3
"""
Test the ISIC code fix
"""
import sys
sys.path.append('/home/<USER>/app')

from emissions_factor_matching.predictions import map_isic_classification
from pydantic import BaseModel

class TestRequest(BaseModel):
    user_query_primary: str
    user_query_secondary: str | None = None

def test_isic_fix():
    print("=== TESTING ISIC CODE FIX ===\n")
    
    # Test transport category
    print("1. Testing transport category:")
    enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
    modifiers = ['diesel', '>32t', 'long-haul', 'heavy-duty']
    
    print(f"   Category: {enhanced_category}")
    print(f"   Modifiers: {modifiers}")
    
    isic_codes = map_isic_classification(enhanced_category, modifiers)
    print(f"   Result: {isic_codes}")
    
    if "4923" in isic_codes:
        print("   ✅ SUCCESS: Found ISIC code 4923!")
    else:
        print("   ❌ FAILED: ISIC code 4923 not found")
    
    print()
    
    # Test chemical category
    print("2. Testing chemical category:")
    enhanced_category = "CHEMICAL_ORGANIC"
    modifiers = ['industrial grade']
    
    print(f"   Category: {enhanced_category}")
    print(f"   Modifiers: {modifiers}")
    
    isic_codes = map_isic_classification(enhanced_category, modifiers)
    print(f"   Result: {isic_codes}")
    
    if len(isic_codes) > 0:
        print(f"   ✅ SUCCESS: Found ISIC codes: {isic_codes}")
    else:
        print("   ❌ FAILED: No ISIC codes found")

if __name__ == "__main__":
    test_isic_fix()
