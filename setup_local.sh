#!/bin/bash

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Create virtual environment
echo "Creating Python virtual environment..."
python3.10 -m venv venv
source venv/bin/activate

# Upgrade pip and install dependencies
echo "Installing dependencies..."
pip install --upgrade pip setuptools wheel
pip install -r dependencies/macos/requirements.txt

# Create necessary directories if they don't exist
echo "Creating cache directories..."
mkdir -p nltk_data
mkdir -p hf_cache
mkdir -p chroma_data
mkdir -p chroma_data_eol
mkdir -p disk_cache
mkdir -p torch_cache

# Setup Redis using Docker
echo "Setting up Redis using Docker..."
CONTAINER_NAME="ml-model-redis"
REDIS_TAG="6"
REDIS_PORT="6379"

# Remove existing container if it exists
if [ "$(docker ps -aq -f name=^${CONTAINER_NAME}$)" ]; then
    echo "Container $CONTAINER_NAME exists. Removing it..."
    docker stop $CONTAINER_NAME
    docker rm -f $CONTAINER_NAME
fi

# Start Redis container
echo "Starting Redis container..."
docker run --name $CONTAINER_NAME \
    -p $REDIS_PORT:6379 \
    -v $(pwd)/scripts/config/redis.conf:/usr/local/etc/redis/redis.conf \
    -d redis:$REDIS_TAG \
    redis-server /usr/local/etc/redis/redis.conf

# Wait for Redis to start
until docker exec $CONTAINER_NAME redis-cli ping > /dev/null 2>&1; do
    echo "Waiting for Redis to start..."
    sleep 2
done

echo "Redis is ready!"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cat > .env << EOL
# Server Configuration
PORT=5001
WORKERS=1
DEBUG=false

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Cache Directories
NLTK_DATA=./nltk_data
HF_CACHE=./hf_cache
CHROMA_DATA=./chroma_data
CHROMA_DATA_EOL=./chroma_data_eol
DISK_CACHE=./disk_cache
TORCH_CACHE=./torch_cache

# API Keys (Add your keys here)
HF_TOKEN=
MAPBOX_ACCESS_TOKEN=
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_API_KEY_US_EAST=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_ENDPOINT_US_EAST=
AZURE_PHI_3_ENDPOINT=
AZURE_PHI_3_KEY=
EOL
    echo "Please edit .env file and add your API keys"
fi

# Set SSL certificate
export SSL_CERT_FILE=$(python -m certifi)

echo "Setup complete! Please:"
echo "1. Edit .env file and add your API keys"
echo "2. Run 'source venv/bin/activate' to activate the virtual environment"
echo "3. Run 'python main.py' to start the application" 