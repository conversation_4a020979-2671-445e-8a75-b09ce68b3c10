import unittest
from unittest.mock import patch, MagicMock
from pydantic import BaseModel
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from emissions_factor_matching.predictions import (
    predict_enhanced_input_category,
    spot_modifiers,
    augment_query_text,
    construct_dynamic_filters,
    re_rank_candidates
)
from emissions_factor_matching import predictions
from emissions_factor_matching.dataset import search_candidates
from emissions_factor_matching.model import Candidate, MatchedEF
from emissions_factor_matching.api import (
    ActivityRecommendationsRequest,
    ActivityResponse,
    get_activity_from_dataset
)
from emissions_factor_matching import geography


class TestRequest(BaseModel):
    chemicalName: str
    lcaLifecycleStage: str | None = None
    geography: str | None = None
    productCategory: str | None = None
    valid_units: list | None = None
    lcia_method: str | None = None
    carbon_only: bool | None = None


class TestPhase1to9CompleteIntegration(unittest.TestCase):
    """Test complete integration of Phases 1.1-1.9: Full AI-assisted pipeline including geography matching and response assembly"""

    def setUp(self):
        """Set up test fixtures for complete pipeline"""
        # Mock ChromaDB response for transport query
        self.mock_transport_response = {
            'ids': [['ef-transport-1', 'ef-transport-2', 'ef-transport-3']],
            'documents': [['transport, freight, lorry >32 metric ton', 'transport, freight, lorry 16-32 metric ton', 'transport, freight, lorry 7.5-16 metric ton']],
            'metadatas': [[
                {
                    'uuid': 'ef-transport-1',
                    'reference_product_name': 'transport, freight, lorry >32 metric ton',
                    'product_information': 'Heavy-duty freight transport by road',
                    'source': 'Ecoinvent 3.11',
                    'ISIC Classification': '4923:Freight transport by road',
                    'isic_section': 'H - Transportation and storage',
                    'activity_type': 'ordinary transforming activity',
                    'geography': 'GLO',
                    'unit': 'tkm'
                },
                {
                    'uuid': 'ef-transport-2',
                    'reference_product_name': 'transport, freight, lorry 16-32 metric ton',
                    'product_information': 'Medium-duty freight transport by road',
                    'source': 'Ecoinvent 3.11',
                    'ISIC Classification': '4923:Freight transport by road',
                    'isic_section': 'H - Transportation and storage',
                    'activity_type': 'ordinary transforming activity',
                    'geography': 'GLO',
                    'unit': 'tkm'
                },
                {
                    'uuid': 'ef-transport-3',
                    'reference_product_name': 'transport, freight, lorry 7.5-16 metric ton',
                    'product_information': 'Light-duty freight transport by road',
                    'source': 'Ecoinvent 3.11',
                    'ISIC Classification': '4923:Freight transport by road',
                    'isic_section': 'H - Transportation and storage',
                    'activity_type': 'ordinary transforming activity',
                    'geography': 'GLO',
                    'unit': 'tkm'
                }
            ]],
            'distances': [[0.154, 0.267, 0.389]]
        }

        # Mock geography database response for Phase 1.8
        self.mock_geography_match = {
            'Activity Name': 'transport, freight, lorry >32 metric ton',
            'Activity UUID': 'geo-matched-transport-1',
            'Reference Product Name': 'transport, freight, lorry >32 metric ton',
            'Product Information': 'Heavy-duty freight transport by road',
            'Source': 'Ecoinvent 3.11',
            'Geography': 'US'  # Geography-matched result
        }

    @patch('emissions_factor_matching.predictions.get_chat_completion')
    @patch('emissions_factor_matching.dataset.collection')
    @patch('emissions_factor_matching.api.get_geography_activity_match')
    @patch('emissions_factor_matching.predictions.map_isic_classification')
    def test_complete_pipeline_phases_1_through_9(self, mock_isic_mapping, mock_geography_match, mock_collection, mock_llm):
        """Test complete pipeline from Phase 1.1 through Phase 1.9 including geography matching and response assembly"""
        logger.info("🚀 Testing COMPLETE AI-assisted pipeline (Phases 1.1-1.9)")

        # Mock LLM responses for each phase
        mock_llm.side_effect = [
            '{"category": "SERVICE_TRANSPORT_ROAD_FREIGHT"}',  # Phase 1.1: Enhanced category (JSON format)
            '["diesel", ">32t", "long-haul", "heavy-duty"]',  # Phase 1.2: Modifiers
            "Heavy-duty diesel freight transport truck over 32 tonnes long-haul road transportation logistics cargo delivery commercial vehicle operations",  # Phase 1.4: Augmented query
            # Phase 1.7: LLM re-ranking response
            """{
                "selected_candidate_uuid": "ef-transport-1",
                "confidence": "HIGH",
                "confidence_score": 0.92,
                "explanation": "This activity directly matches the user's request for heavy cargo truck transportation. The >32 metric ton specification aligns perfectly with the heavy cargo requirement, and the diesel fuel type is the standard for this vehicle class. The activity represents the exact transportation service requested with appropriate geographic scope and operational characteristics.",
                "ranking_rationale": "Ranked based on vehicle weight class match (>32t), fuel type alignment (diesel), and service type precision (freight transport). The heavy-duty specification in the user query strongly indicates the need for the largest vehicle category.",
                "alternative_considerations": "Other candidates represent smaller vehicle classes (16-32t, 7.5-16t) which would be less suitable for heavy cargo applications requiring maximum payload capacity."
            }"""
        ]

        # Mock ChromaDB response
        mock_collection.query.return_value = self.mock_transport_response

        # Mock geography matching response
        mock_geography_match.return_value = self.mock_geography_match

        # Mock ISIC mapping response
        mock_isic_mapping.return_value = ["4923"]

        # Create test request
        request = TestRequest(
            chemicalName="truck transportation for heavy cargo",
            lcaLifecycleStage="use",
            geography="US",
            valid_units=["tkm"]
        )

        logger.info(f"📝 Input: '{request.chemicalName}' (ISO: {request.geography})")

        # Phase 1.1: Enhanced Input Category Prediction
        enhanced_category = predict_enhanced_input_category(request)
        logger.info(f"🎯 Phase 1.1 - Enhanced Category: {enhanced_category}")
        self.assertEqual(enhanced_category, "SERVICE_TRANSPORT_ROAD_FREIGHT")

        # Phase 1.2: Modifier Spotting
        modifiers = spot_modifiers(request, enhanced_category)
        logger.info(f"🔍 Phase 1.2 - Modifiers: {modifiers}")
        # Check that we got multiple relevant modifiers
        self.assertGreaterEqual(len(modifiers), 3)
        # Check for transport-related modifiers (any of these would be valid)
        transport_modifiers = ["diesel", "heavy duty", "heavy-duty", ">32t", "truck", "freight", "cargo"]
        found_transport_modifier = any(mod in modifiers for mod in transport_modifiers)
        self.assertTrue(found_transport_modifier, f"Expected at least one transport modifier from {transport_modifiers}, but got {modifiers}")

        # Phase 1.3: ISIC Classification Mapping
        isic_codes = predictions.map_isic_classification(enhanced_category, modifiers, request.chemicalName)
        logger.info(f"🏭 Phase 1.3 - ISIC Codes: {isic_codes}")
        self.assertEqual(isic_codes, ["4923"])

        # Phase 1.4: Query Text Augmentation
        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
        logger.info(f"📈 Phase 1.4 - Augmented Query: '{augmented_query}'")
        # Check for relevant transport concepts in the augmented query
        augmented_lower = augmented_query.lower()
        transport_concepts = ["heavy", "truck", "freight", "transport", "cargo", "vehicle"]
        found_concepts = [concept for concept in transport_concepts if concept in augmented_lower]
        self.assertGreaterEqual(len(found_concepts), 2, f"Expected at least 2 transport concepts from {transport_concepts} in augmented query")

        # Phase 1.5: Dynamic Filter Construction
        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
        logger.info(f"🔧 Phase 1.5 - Dynamic Filters: {filters}")

        # Phase 1.6: ChromaDB Vector Search
        candidates = search_candidates(augmented_query, filters, n_results=10)
        logger.info(f"🔍 Phase 1.6 - Found {len(candidates)} candidates")
        self.assertEqual(len(candidates), 3)
        self.assertIsInstance(candidates[0], Candidate)

        # Phase 1.7: LLM Re-ranking & Justification
        matched_ef = re_rank_candidates(
            request_model=request,
            candidates=candidates,
            augmented_query=augmented_query,
            enhanced_category=enhanced_category,
            modifiers=modifiers,
            isic_codes=isic_codes
        )
        logger.info(f"🧠 Phase 1.7 - LLM Re-ranking Complete: Selected '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")

        # Verify Phase 1.7 results
        self.assertIsInstance(matched_ef, MatchedEF)
        self.assertEqual(matched_ef.activity_uuid, "ef-transport-1")
        self.assertEqual(matched_ef.confidence, "HIGH")
        self.assertEqual(matched_ef.confidence_score, 0.92)

        # Phase 1.8: Geography Matching & Record Retrieval
        logger.info("🌍 Phase 1.8 - Geography Matching & Record Retrieval - Starting")
        matched_activity = get_activity_from_dataset(
            activity_name=matched_ef.activity_name,
            iso_code=request.geography,
            similarity=matched_ef.confidence_score or 0.0,
            reference_product_name=matched_ef.reference_product_name,
            preferred_source=matched_ef.source  # Include preferred_source to match API behavior
        )
        logger.info(f"🌍 Phase 1.8 Complete: Geography matched to '{matched_activity.geography}'")

        # Verify Phase 1.8 results
        self.assertEqual(matched_activity.activity_uuid, "geo-matched-transport-1")
        self.assertEqual(matched_activity.geography, "US")
        self.assertEqual(matched_activity.activity_name, "transport, freight, lorry >32 metric ton")
        self.assertEqual(matched_activity.similarity, 0.92)

        # Phase 1.9: Response Assembly
        logger.info("📦 Phase 1.9 - Response Assembly - Starting")

        # Create recommendations from remaining candidates (excluding the selected one)
        recommendations = []
        for candidate in candidates:
            if candidate.activity_uuid != matched_ef.activity_uuid:
                try:
                    # Mock additional geography matches for recommendations
                    mock_rec_geography = {
                        'Activity Name': candidate.activity_name,
                        'Activity UUID': f"geo-rec-{candidate.activity_uuid}",  # Make unique for recommendations
                        'Reference Product Name': candidate.reference_product_name,
                        'Product Information': candidate.product_information,
                        'Source': candidate.source,
                        'Geography': 'US'
                    }

                    with patch('emissions_factor_matching.api.get_geography_activity_match', return_value=mock_rec_geography):
                        recommendation = get_activity_from_dataset(
                            activity_name=candidate.activity_name,
                            iso_code=request.geography,
                            similarity=candidate.similarity_score or 0.0,
                            reference_product_name=candidate.reference_product_name
                        )
                        recommendations.append(recommendation)
                except Exception as e:
                    logger.warning(f"Failed to create recommendation for {candidate.activity_uuid}: {str(e)}")
                    continue

        logger.info(f"📦 Phase 1.9 Complete: Assembled response with {len(recommendations)} recommendations")

        # Assemble final response
        final_response = ActivityResponse(
            matched_activity=matched_activity,
            confidence=matched_ef.confidence,
            explanation=matched_ef.explanation,
            recommendations=recommendations,
        )

        # Verify Phase 1.9 results
        self.assertIsInstance(final_response, ActivityResponse)
        self.assertEqual(final_response.matched_activity.activity_uuid, "geo-matched-transport-1")
        self.assertEqual(final_response.confidence, "HIGH")

        # Test for transportation concepts (LLM-friendly validation)
        explanation_lower = final_response.explanation.lower()
        transport_concepts = ["transport", "freight", "truck", "lorry"]
        self.assertTrue(
            any(concept in explanation_lower for concept in transport_concepts),
            f"Explanation should mention transportation concepts. Got: {final_response.explanation}"
        )

        # Test for weight/size concepts
        weight_concepts = ["32", "heavy", "ton", "tonne", "cargo"]
        self.assertTrue(
            any(concept in explanation_lower for concept in weight_concepts),
            f"Explanation should mention weight/cargo concepts. Got: {final_response.explanation}"
        )

        self.assertEqual(len(final_response.recommendations), 2)  # 2 alternative recommendations

        # Verify recommendations are properly geography-matched
        for rec in final_response.recommendations:
            self.assertEqual(rec.geography, "US")
            self.assertNotEqual(rec.activity_uuid, final_response.matched_activity.activity_uuid)

        # Verify geography matching was called correctly (with optional 4th parameter)
        # Note: matched_ef.source may be None if not provided in LLM response
        mock_geography_match.assert_called_with(
            matched_ef.activity_name,
            request.geography,
            matched_ef.reference_product_name,
            matched_ef.source  # This will be None if not in LLM response
        )

        logger.info(f"🏆 Final Selection: '{final_response.matched_activity.activity_name}' (UUID: {final_response.matched_activity.activity_uuid})")
        logger.info(f"🌍 Geography: {final_response.matched_activity.geography}")
        logger.info(f"🎯 Confidence: {final_response.confidence}")
        logger.info(f"📋 Recommendations: {len(final_response.recommendations)} alternatives")
        logger.info(f"💡 Explanation: {final_response.explanation[:100]}...")
        logger.info("✅ COMPLETE pipeline (Phases 1.1-1.9) test passed!")

    @patch('emissions_factor_matching.predictions.get_chat_completion')
    @patch('emissions_factor_matching.dataset.collection')
    @patch('emissions_factor_matching.api.get_geography_activity_match')
    @patch('emissions_factor_matching.predictions.map_isic_classification')
    def test_complete_pipeline_with_geography_fallback(self, mock_isic_mapping, mock_geography_match, mock_collection, mock_llm):
        """Test complete pipeline with geography fallback scenario"""
        logger.info("🌍 Testing complete pipeline with geography fallback")

        # Mock LLM responses
        mock_llm.side_effect = [
            '{"category": "MATERIAL_PRODUCTION_STEEL"}',  # Phase 1.1: Enhanced category (JSON format)
            '["carbon steel", "hot-rolled"]',
            "Carbon steel hot-rolled production manufacturing metal alloy industrial material",
            """{
                "selected_candidate_uuid": "ef-steel-1",
                "confidence": "MEDIUM",
                "confidence_score": 0.75,
                "explanation": "This steel production activity matches the user's request for carbon steel manufacturing. The hot-rolled specification aligns with typical steel processing methods.",
                "ranking_rationale": "Selected based on material type match and processing method alignment.",
                "alternative_considerations": "Other steel grades available but carbon steel is most common for general applications."
            }"""
        ]

        # Mock ChromaDB response for steel query
        mock_steel_response = {
            'ids': [['ef-steel-1']],
            'documents': [['steel production, carbon steel, hot-rolled']],
            'metadatas': [[{
                'uuid': 'ef-steel-1',
                'reference_product_name': 'steel, low-alloyed, hot rolled',
                'product_information': 'Carbon steel production',
                'source': 'Ecoinvent 3.11',
                'ISIC Classification': '2410:Manufacture of basic iron and steel',
                'isic_section': 'C - Manufacturing',
                'activity_type': 'ordinary transforming activity',
                'geography': 'GLO',
                'unit': 'kg'
            }]],
            'distances': [[0.234]]
        }

        mock_collection.query.return_value = mock_steel_response

        # Mock geography fallback (no exact match, falls back to GLO)
        mock_geography_fallback = {
            'Activity Name': 'steel, low-alloyed, hot rolled',
            'Activity UUID': 'geo-fallback-steel-1',
            'Reference Product Name': 'steel, low-alloyed, hot rolled',
            'Product Information': 'Carbon steel production',
            'Source': 'Ecoinvent 3.11',
            'Geography': 'GLO'  # Fallback to global geography
        }
        mock_geography_match.return_value = mock_geography_fallback

        # Mock ISIC mapping response
        mock_isic_mapping.return_value = ["2410"]

        # Create test request for a region without specific steel data
        request = TestRequest(
            chemicalName="carbon steel production",
            geography="ZW",  # Zimbabwe - likely to fallback to GLO
            valid_units=["kg"]
        )

        logger.info(f"📝 Input: '{request.chemicalName}' (ISO: {request.geography})")

        # Run through complete pipeline
        enhanced_category = predict_enhanced_input_category(request)
        modifiers = spot_modifiers(request, enhanced_category)
        isic_codes = predictions.map_isic_classification(enhanced_category, modifiers, request.chemicalName)
        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
        candidates = search_candidates(augmented_query, filters, n_results=10)
        matched_ef = re_rank_candidates(request, candidates, augmented_query, enhanced_category, modifiers, isic_codes)

        # Phase 1.8: Geography Matching with fallback
        matched_activity = get_activity_from_dataset(
            activity_name=matched_ef.activity_name,
            iso_code=request.geography,
            similarity=matched_ef.confidence_score or 0.0,
            reference_product_name=matched_ef.reference_product_name
        )

        # Phase 1.9: Response Assembly
        final_response = ActivityResponse(
            matched_activity=matched_activity,
            confidence=matched_ef.confidence,
            explanation=matched_ef.explanation,
            recommendations=[],  # No additional candidates in this test
        )

        # Verify geography fallback worked
        self.assertEqual(final_response.matched_activity.geography, "GLO")
        # LLM confidence can vary - accept any valid confidence level
        self.assertIn(final_response.confidence, ["LOW", "MEDIUM", "HIGH"])

        # Test for steel production concepts (LLM-friendly validation)
        explanation_lower = final_response.explanation.lower()
        steel_concepts = ["steel", "production", "manufacturing", "carbon", "hot-rolled"]
        self.assertTrue(
            any(concept in explanation_lower for concept in steel_concepts),
            f"Explanation should mention steel production concepts. Got: {final_response.explanation}"
        )

        # Test for industrial/manufacturing concepts
        industrial_concepts = ["industrial", "manufacturing", "process", "material", "metal"]
        self.assertTrue(
            any(concept in explanation_lower for concept in industrial_concepts),
            f"Explanation should mention industrial/manufacturing concepts. Got: {final_response.explanation}"
        )

        logger.info(f"🌍 Geography fallback: {request.geography} → {final_response.matched_activity.geography}")
        logger.info("✅ Complete pipeline with geography fallback test passed!")

    def test_pipeline_data_flow_integrity(self):
        """Test that data flows correctly through all phases without mocking"""
        logger.info("🔄 Testing pipeline data flow integrity")

        # Create a simple request
        request = TestRequest(
            chemicalName="electricity production",
            geography="US"
        )

        # Test that each phase can handle the request structure
        self.assertIsInstance(request.chemicalName, str)
        self.assertIsInstance(request.geography, str)

        # Verify request model compatibility with API
        api_request = ActivityRecommendationsRequest(
            chemicalName=request.chemicalName,
            geography=request.geography
        )
        self.assertEqual(api_request.chemicalName, request.chemicalName)
        self.assertEqual(api_request.geography, request.geography)

        logger.info("✅ Pipeline data flow integrity test passed!")


if __name__ == '__main__':
    unittest.main()
