import numpy as np
from typing import Optional
from pydantic import BaseModel
from fastapi import (
    FastAPI,
    Head<PERSON>,
    Depends,
    Query,
    HTTPException,
)
from torch import tensor
import json
from emissions_factor_matching.dataset import collection, collection_eol
from emissions_factor_matching.predictions import (
    predict_enhanced_input_category,
    spot_modifiers,
    map_isic_classification,
    augment_query_text,
    construct_dynamic_filters,
    re_rank_candidates,
    get_closest_match,
    get_technological_representation,
)
from emissions_factor_matching.dataset import search_candidates_with_fallback
from shared_predictions import get_top_match
from emissions_factor_matching.geography import get_geography_activity_match
from utils import logger
from utils.cache import create_cache, cached
import asyncio

cache = create_cache(prefix='ef_')

def get_api_version(api_version: Optional[str] = Header(default="latest")) -> str:
    return api_version

model_api = FastAPI()

def query_eol_activities(material: str, disposal_method: str, number_of_results: int = 25):
    where_document = (
        {"$contains":"Recycled Content cut-off"}
        if disposal_method == "recycling"
        else None
    )

    return collection_eol.query(
        query_texts=[f"{material} {disposal_method}"],
        n_results=number_of_results,
        where_document=where_document
    )

class ActivityRequest(BaseModel):
    chemical_name: str

class Activity(BaseModel):
    activity_uuid: str
    activity_name: str
    reference_product_name: str
    product_information: str
    source: str
    geography: str | None = None

class ActivityWithSimilarity(Activity):
    similarity: float | None = None

class ActivityRecommendationsRequest(BaseModel):
    # Frontend GraphQL parameters (match exactly)
    chemicalName: str | None = None  # Optional to support backward compatibility
    productCategory: str | None = None
    geography: str | None = None
    geographyModeling: bool | None = None
    unit: str | None = None
    lcaLifecycleStage: str | None = None
    labs: bool | None = None  # Lab-specific parameter

    # Legacy parameters for backward compatibility (from old API calls)
    chemical_name: str | None = None
    iso_code: str | None = None
    product_category: str | None = None

    # Critical filtering parameters (used by construct_dynamic_filters)
    valid_units: list[str] | None = None
    number_of_matches: int | None = None
    lcia_method: str | None = None
    carbon_only: bool | None = None
    
    # Debug/testing parameter
    include_phase_outputs: bool | None = None

class ActivityResponse(BaseModel):
    matched_activity: Activity
    confidence: str
    explanation: str
    recommendations: list[Activity]

class PhaseOutputs(BaseModel):
    enhanced_category: str
    modifiers: list[str]
    isic_codes: list[str]
    augmented_query: str
    filters: dict
    vector_search_results: list[dict]
    llm_ranking: dict
    geography_match: dict
    recommendations: list[dict]

class ActivityResponseWithPhases(ActivityResponse):
    phase_outputs: PhaseOutputs | None = None

def get_activity_from_dataset(
    activity_name: str,
    iso_code: str,
    similarity: float,
    reference_product_name: str,
    preferred_source: str | None = None,
) -> ActivityWithSimilarity:
    activity = get_geography_activity_match(
        activity_name,
        iso_code,
        reference_product_name,
        preferred_source
    )

    return ActivityWithSimilarity(
        activity_name=activity["Activity Name"],
        activity_uuid=activity["Activity UUID"],
        reference_product_name=activity["Reference Product Name"],
        product_information=activity["Product Information"],
        source=activity["Source"],
        geography=activity["Geography"],
        similarity=similarity
    )

@model_api.get("/search")
@cached(cache)
def search_activities(
    query: str = Query(..., description="The search query"),
    geography_iso3: str = Query(default="GLO", description="The geography to search in"),
    number_of_results: int = Query(default=25, description="The number of results to return"),
    api_version: str = Depends(get_api_version),
) -> list[ActivityWithSimilarity]:
    logger.info(f"API Version: {api_version}")

    where = {"activity_type": {"$eq": "ordinary transforming activity"}}
    documents = collection.query(
        query_texts=[query],
        n_results=number_of_results,
        where=where,
    )

    activities = [
        get_activity_from_dataset(
            activity_name=documents["documents"][0][i],
            iso_code=geography_iso3,
            similarity=0,
            reference_product_name=documents["metadatas"][0][i]["reference_product_name"]
        )
        for i in range(len(documents["ids"][0]))
    ]

    return activities

class EolActivityRecommendationsRequest(BaseModel):
    material: str
    disposal_method: str
    geography: str = "RoW"

@model_api.post("/eol/activity")
@cached(cache)
async def get_eol_recommended_activity(
    activity_request: EolActivityRecommendationsRequest,
    api_version: str = Depends(get_api_version),
):
    logger.info(f"API Version: {api_version}")

    documents = query_eol_activities(
        material=activity_request.material,
        disposal_method=activity_request.disposal_method,
    )

    activities = []
    activities_for_selection = []
    for i in range(len(documents["ids"][0])):
        activity = {
            "uuid": documents["metadatas"][0][i]["uuid"],
            "activity_name": documents["documents"][0][i],
            "metadata": documents["metadatas"][0][i],
            "similarity": 0,
        }
        activities.append(activity)
        activities_for_selection.append({
            k: v
            for k, v in activity.items()
            if k != "similarity"
        })

    closest_match = await get_closest_match(
        f"{activity_request.material} ({activity_request.disposal_method})",
        activities_for_selection,
    )

    matched_activity = next(
        (
            get_activity_from_dataset(
                activity["activity_name"],
                activity_request.geography,
                activity["similarity"],
                activity["metadata"]["reference_product_name"]
            )
            for activity in activities
            if closest_match.get("activity_uuid") == activity["uuid"]
        ),
        None,
    )

    return matched_activity

@model_api.post("/activities/recommendations")
# @cached(cache)  # Temporarily disabled for testing
async def get_recommended_activities(
    activity_request: ActivityRecommendationsRequest,
    api_version: str = Depends(get_api_version),
):
    logger.info(f"API Version: {api_version}")
    logger.info(f"DEBUG: Received request parameters: {activity_request.model_dump()}")
    logger.info("Phase 1: Enhanced Input Category Prediction - Starting")

    # Handle backward compatibility
    if not activity_request.chemicalName and activity_request.chemical_name:
        activity_request.chemicalName = activity_request.chemical_name
        logger.info(f"Backward compatibility: using chemical_name")

    if not activity_request.chemicalName:
        raise HTTPException(status_code=400, detail="chemicalName or chemical_name is required")
    
    # Handle backward compatibility for geography
    if not activity_request.geography and activity_request.iso_code:
        activity_request.geography = activity_request.iso_code
    elif not activity_request.geography:
        activity_request.geography = "GLO"
    
    # Handle backward compatibility for product category
    if not activity_request.productCategory and activity_request.product_category:
        activity_request.productCategory = activity_request.product_category

    # Phase 1.1: Enhanced Input Category Prediction
    enhanced_category = predict_enhanced_input_category(activity_request)
    logger.info(f"Phase 1 Complete: Enhanced category = {enhanced_category}")

    # Phase 1.2: Modifier Spotting
    logger.info("Phase 2: Modifier Spotting - Starting")
    modifiers = spot_modifiers(activity_request, enhanced_category)
    logger.info(f"Phase 2 Complete: Extracted {len(modifiers)} modifiers = {modifiers}")

    # Phase 1.3: ISIC Classification Mapping
    logger.info("Phase 3: ISIC Classification Mapping - Starting")
    isic_codes = map_isic_classification(enhanced_category, modifiers, activity_request.chemicalName)
    logger.info(f"Phase 3 Complete: Mapped to {len(isic_codes)} ISIC codes = {isic_codes}")

    # Phase 1.4: Query Text Augmentation
    logger.info("Phase 4: Query Text Augmentation - Starting")
    augmented_query = augment_query_text(activity_request, enhanced_category, modifiers, isic_codes)
    logger.info(f"Phase 4 Complete: Augmented query = '{augmented_query}'")

    # Phase 1.5: Dynamic Filter Construction
    logger.info("Phase 5: Dynamic Filter Construction - Starting")
    try:
        logger.info(f"DEBUG: activity_request for filtering: {activity_request.model_dump()}")
    except Exception as e:
        logger.error(f"DEBUG: Failed to dump activity_request: {e}")
    dynamic_filters = construct_dynamic_filters(activity_request, enhanced_category, modifiers, isic_codes)
    logger.info(f"Phase 5 Complete: Dynamic filters = {dynamic_filters}")

    # Phase 1.6: ChromaDB Vector Search Enhancement
    logger.info("Phase 6: ChromaDB Vector Search Enhancement - Starting")
    # Use number_of_matches if provided, otherwise default to 25
    n_results = activity_request.number_of_matches or 25
    candidates = search_candidates_with_fallback(
        augmented_query=augmented_query,
        filters=dynamic_filters,
        n_results=n_results
    )
    logger.info(f"Phase 6 Complete: Retrieved {len(candidates)} candidates (requested: {n_results})")

    # Phase 1.7: LLM Re-ranking & Justification
    logger.info("Phase 7: LLM Re-ranking & Justification - Starting")
    if not candidates:
        raise HTTPException(status_code=404, detail="No emission factor candidates found")

    matched_ef = re_rank_candidates(
        request_model=activity_request,
        candidates=candidates,
        augmented_query=augmented_query,
        enhanced_category=enhanced_category,
        modifiers=modifiers,
        isic_codes=isic_codes
    )
    logger.info(f"Phase 7 Complete: Selected '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")

    # Phase 1.8: Geography Matching & Record Retrieval
    logger.info("Phase 8: Geography Matching & Record Retrieval - Starting")
    logger.info(f"Phase 8: Attempting to match geography '{activity_request.geography}' for activity '{matched_ef.activity_name}'")
    logger.info(f"Phase 8: Preferred source: '{matched_ef.source}'")
    
    matched_activity_with_similarity = get_activity_from_dataset(
        activity_name=matched_ef.activity_name,
        iso_code=activity_request.geography,
        similarity=matched_ef.confidence_score or 0.0,
        reference_product_name=matched_ef.reference_product_name,
        preferred_source=None  # Let geography matching use any available source
    )
    # Convert to Activity (without similarity) for backend compatibility
    matched_activity = Activity(
        activity_uuid=matched_activity_with_similarity.activity_uuid,
        activity_name=matched_activity_with_similarity.activity_name,
        reference_product_name=matched_activity_with_similarity.reference_product_name,
        product_information=matched_activity_with_similarity.product_information,
        source=matched_activity_with_similarity.source,
        geography=matched_activity_with_similarity.geography
    )
    logger.info(f"Phase 8: Final activity UUID: {matched_activity.activity_uuid}")
    logger.info(f"Phase 8: Similarity score preserved: {matched_activity_with_similarity.similarity:.4f}")
    logger.info(f"Phase 8 Complete: Geography matched to '{matched_activity.geography}' with source '{matched_activity.source}'")

    # Phase 1.9: Response Assembly
    logger.info("Phase 9: Response Assembly - Starting")

    # Create recommendations from remaining candidates (excluding the selected one)
    recommendations = []
    for candidate in candidates:
        if candidate.activity_uuid != matched_ef.activity_uuid:
            try:
                recommendation_with_similarity = get_activity_from_dataset(
                    activity_name=candidate.activity_name,
                    iso_code=activity_request.geography,
                    similarity=candidate.similarity_score or 0.0,
                    reference_product_name=candidate.reference_product_name,
                    preferred_source=None  # Let geography matching use any available source
                )
                # Convert to Activity (without similarity) for backend compatibility
                recommendation = Activity(
                    activity_uuid=recommendation_with_similarity.activity_uuid,
                    activity_name=recommendation_with_similarity.activity_name,
                    reference_product_name=recommendation_with_similarity.reference_product_name,
                    product_information=recommendation_with_similarity.product_information,
                    source=recommendation_with_similarity.source,
                    geography=recommendation_with_similarity.geography
                )
                recommendations.append(recommendation)
            except Exception as e:
                logger.warning(f"Phase 8: Failed to create recommendation for {candidate.activity_uuid}: {str(e)}")
                continue

    logger.info(f"Phase 9 Complete: Assembled response with {len(recommendations)} recommendations")

    # Check if phase outputs are requested
    include_phase_outputs = getattr(activity_request, 'include_phase_outputs', False)
    
    if include_phase_outputs:
        # Prepare phase outputs data
        phase_outputs = PhaseOutputs(
            enhanced_category=enhanced_category,
            modifiers=modifiers,
            isic_codes=isic_codes,
            augmented_query=augmented_query,
            filters=dynamic_filters,
            vector_search_results=[{
                'activity_name': c.activity_name,
                'activity_uuid': c.activity_uuid,
                'similarity_score': c.similarity_score or 0.0,
                'reference_product_name': c.reference_product_name
            } for c in candidates[:10]],  # Limit to top 10 for response size
            llm_ranking={
                'selected_uuid': matched_ef.activity_uuid,
                'activity_name': matched_ef.activity_name,
                'confidence': matched_ef.confidence,
                'confidence_score': matched_ef.confidence_score or 0.0,
                'explanation': matched_ef.explanation
            },
            geography_match={
                'matched_geography': matched_activity.geography,
                'original_request': activity_request.geography,
                'similarity': matched_activity_with_similarity.similarity
            },
            recommendations=[{
                'activity_uuid': rec.activity_uuid,
                'activity_name': rec.activity_name,
                'geography': rec.geography
            } for rec in recommendations[:5]]  # Limit to top 5
        )
        
        return ActivityResponseWithPhases(
            matched_activity=matched_activity,
            confidence=matched_ef.confidence.lower(),  # Convert to lowercase for backend compatibility
            explanation=matched_ef.explanation,
            recommendations=recommendations,
            phase_outputs=phase_outputs
        )
    else:
        return ActivityResponse(
            matched_activity=matched_activity,
            confidence=matched_ef.confidence.lower(),  # Convert to lowercase for backend compatibility
            explanation=matched_ef.explanation,
            recommendations=recommendations,
        )

@model_api.get("/geography-match")
@cached(cache)
def geography_match(
    activity_name: str,
    reference_product_name: str,
    target_geography_iso: str,
    api_version: str = Depends(get_api_version),
) -> Activity:
    activity = get_geography_activity_match(
        activity_name,
        target_geography_iso,
        reference_product_name
    )

    return Activity(
        activity_name=activity["Activity Name"],
        activity_uuid=activity["Activity UUID"],
        reference_product_name=activity["Reference Product Name"],
        product_information=activity["Product Information"],
        source=activity["Source"],
        geography=activity["Geography"],
    )

@model_api.post("/cache/clear")
def clear_cache():
    """Clear the entire cache."""
    cache.clear()
    return {"message": "Cache cleared successfully"}

@model_api.get("/cache/stats")
def get_cache_stats():
    """Get cache statistics."""
    volume = cache.volume()
    size_kb = volume / 1024 if volume is not None else 0
    try:
        num_items = len(cache)
    except (TypeError, NotImplementedError):
        num_items = "Not available for this cache type"

    return {
        "size (KB)": size_kb,
        "number of items": num_items,
        "directory": cache.directory
    }

@model_api.get("/match-quality")
@cached(cache)
async def match_quality(process_name: str = Query(...), matched_activity_name: str = Query(...)):
    technology_match = await get_technological_representation(process_name, matched_activity_name)

    return {"match_quality": technology_match}
