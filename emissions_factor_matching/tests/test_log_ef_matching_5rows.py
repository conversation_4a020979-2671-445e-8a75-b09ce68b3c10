import csv
import requests
import json
from datetime import datetime
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Configuration ---
API_ENDPOINT_URL = "http://localhost:5001/api/emissions-factor-matching/activities/recommendations" # For local testing
# API_ENDPOINT_URL = "https://api.carbonbright.com/api/emissions-factor-matching/activities/recommendations" # Production URL
INPUT_CSV_FILENAME = 'test_combined_input_data_cleaned.csv'
# Create an 'output' directory if it doesn't exist
OUTPUT_DIR = 'output_api_runs' # Changed directory name for clarity
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)
OUTPUT_CSV_FILENAME = os.path.join(OUTPUT_DIR, f'api_results_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv')

# Set maximum rows to process for testing
MAX_ROWS_TO_PROCESS = 5  # Only process first 5 rows for testing

def validate_api_response(response_data):
    """Validate the structure of API response"""
    required_fields = ['matched_activity', 'confidence', 'explanation', 'recommendations']
    for field in required_fields:
        if field not in response_data:
            raise ValueError(f"API response missing required field: {field}")
    
    if not isinstance(response_data['matched_activity'], dict):
        raise ValueError("matched_activity must be a dictionary")
    
    if not isinstance(response_data['recommendations'], list):
        raise ValueError("recommendations must be a list")

def log_api_calls():
    """
    Reads the input CSV, calls the API for each row, and writes API results to an output CSV
    for manual review.
    """
    processed_rows_data = []
    
    # Define the header for the output CSV
    # We're keeping original columns and adding API response columns
    output_header = [
        'Category', 'Input_ManufacturingMethod', 'Expected_EF_ActivityName', # Original data
        'Actual_System_EF_ActivityName', 'Actual_System_Confidence', 'Actual_System_Explanation', 
        'Actual_System_Recommendations_Short', 'API_Status', # API results & status
        # Phase-wise output columns
        'Phase1_EnhancedCategory', 'Phase2_Modifiers', 'Phase3_ISICCodes',
        'Phase4_AugmentedQuery', 'Phase5_Filters', 'Phase6_VectorSearchResults',
        'Phase7_LLMRanking', 'Phase8_GeographyMatch', 'Phase9_Recommendations'
    ]

    try:
        with open(INPUT_CSV_FILENAME, 'r', encoding='utf-8-sig') as infile:
            reader = csv.DictReader(infile)
            if not reader.fieldnames or not all(col in reader.fieldnames for col in ['Category', 'Input_ManufacturingMethod', 'Expected_EF_ActivityName']):
                logger.error(f"Input CSV '{INPUT_CSV_FILENAME}' is missing required columns")
                return

            for i, input_row in enumerate(reader):
                # Process only first 5 rows
                if i >= MAX_ROWS_TO_PROCESS:
                    logger.info(f"Reached maximum rows to process ({MAX_ROWS_TO_PROCESS}). Stopping.")
                    break

                logger.info(f"Processing row {i+1}: {input_row.get('Input_ManufacturingMethod')[:50]}...")
                
                current_output_data = {
                    'Category': input_row.get('Category', ''),
                    'Input_ManufacturingMethod': input_row.get('Input_ManufacturingMethod', ''),
                    'Expected_EF_ActivityName': input_row.get('Expected_EF_ActivityName', ''),
                    'Actual_System_EF_ActivityName': '',
                    'Actual_System_Confidence': '',
                    'Actual_System_Explanation': '',
                    'Actual_System_Recommendations_Short': '',
                    'API_Status': '',
                    # Initialize phase-wise output columns
                    'Phase1_EnhancedCategory': '',
                    'Phase2_Modifiers': '',
                    'Phase3_ISICCodes': '',
                    'Phase4_AugmentedQuery': '',
                    'Phase5_Filters': '',
                    'Phase6_VectorSearchResults': '',
                    'Phase7_LLMRanking': '',
                    'Phase8_GeographyMatch': '',
                    'Phase9_Recommendations': ''
                }

                if not current_output_data['Input_ManufacturingMethod']:
                    current_output_data['API_Status'] = "Skipped - Input_ManufacturingMethod is empty"
                    processed_rows_data.append(current_output_data)
                    continue

                api_payload = {
                    "chemicalName": current_output_data['Input_ManufacturingMethod'],
                    "productCategory": current_output_data['Category'],
                    "number_of_matches": 10,  # Request more matches for better recommendations
                    "include_phase_outputs": True  # Request phase-wise outputs
                }

                try:
                    # Add API version header and handle both new and old parameter names
                    headers = {
                        "api-version": "beta",
                        "Content-Type": "application/json"
                    }
                    
                    # Try with new parameter names first
                    response = requests.post(API_ENDPOINT_URL, json=api_payload, headers=headers, timeout=45)
                    
                    # If that fails with 400, try with old parameter names
                    if response.status_code == 400:
                        old_payload = {
                            "chemical_name": current_output_data['Input_ManufacturingMethod'],
                            "product_category": current_output_data['Category']
                        }
                        response = requests.post(API_ENDPOINT_URL, json=old_payload, headers=headers, timeout=45)
                    
                    response.raise_for_status()
                    
                    api_data = response.json()
                    validate_api_response(api_data)
                    
                    # Debug: Log the response structure
                    logger.info(f"DEBUG: Response keys: {list(api_data.keys())}")
                    logger.info(f"DEBUG: Has phase_outputs: {'phase_outputs' in api_data}")

                    # Extract phase-wise outputs if available
                    if 'phase_outputs' in api_data:
                        phase_outputs = api_data['phase_outputs']
                        logger.info(f"DEBUG: Found phase_outputs with keys: {list(phase_outputs.keys()) if phase_outputs else 'None'}")
                        current_output_data.update({
                            'Phase1_EnhancedCategory': phase_outputs.get('enhanced_category', ''),
                            'Phase2_Modifiers': ', '.join(phase_outputs.get('modifiers', [])),
                            'Phase3_ISICCodes': ', '.join(phase_outputs.get('isic_codes', [])),
                            'Phase4_AugmentedQuery': phase_outputs.get('augmented_query', ''),
                            'Phase5_Filters': json.dumps(phase_outputs.get('filters', {})),
                            'Phase6_VectorSearchResults': json.dumps([{
                                'activity_name': c.get('activity_name', ''),
                                'similarity_score': c.get('similarity_score', 0)
                            } for c in phase_outputs.get('vector_search_results', [])]),
                            'Phase7_LLMRanking': json.dumps({
                                'selected_uuid': phase_outputs.get('llm_ranking', {}).get('selected_uuid', ''),
                                'confidence': phase_outputs.get('llm_ranking', {}).get('confidence', ''),
                                'explanation': phase_outputs.get('llm_ranking', {}).get('explanation', '')
                            }),
                            'Phase8_GeographyMatch': json.dumps({
                                'matched_geography': phase_outputs.get('geography_match', {}).get('matched_geography', ''),
                                'similarity': phase_outputs.get('geography_match', {}).get('similarity', 0)
                            }),
                            'Phase9_Recommendations': json.dumps([{
                                'activity_name': r.get('activity_name', ''),
                                'confidence': r.get('confidence', '')
                            } for r in phase_outputs.get('recommendations', [])])
                        })

                    matched_activity = api_data.get("matched_activity")
                    if matched_activity and isinstance(matched_activity, dict) and matched_activity.get("activity_name"):
                        current_output_data['Actual_System_EF_ActivityName'] = matched_activity.get("activity_name")
                    else:
                        current_output_data['Actual_System_EF_ActivityName'] = "N/A - No Match Found by System"
                    
                    current_output_data['Actual_System_Confidence'] = str(api_data.get("confidence", ""))
                    current_output_data['Actual_System_Explanation'] = str(api_data.get("explanation", ""))

                    recommendations = api_data.get("recommendations", [])
                    if recommendations and isinstance(recommendations, list):
                        rec_names_short = []
                        for rec_idx, rec in enumerate(recommendations[:3]): # Log top 3 recommendations
                            if isinstance(rec, dict) and rec.get("activity_name"):
                                rec_names_short.append(f"{rec_idx+1}: {rec.get('activity_name')}")
                        current_output_data['Actual_System_Recommendations_Short'] = " | ".join(rec_names_short)
                    
                    current_output_data['API_Status'] = "Success"

                except requests.exceptions.HTTPError as http_err:
                    if response.status_code == 404:
                        current_output_data['API_Status'] = "No emission factor candidates found"
                        current_output_data['Actual_System_EF_ActivityName'] = "N/A - No Matches Found"
                    else:
                        current_output_data['API_Status'] = f"API HTTP Error: {http_err} - Response: {response.text[:200]}"
                    logger.error(f"HTTP Error for row {i+1}: {http_err}")
                except requests.exceptions.ConnectionError as conn_err:
                    current_output_data['API_Status'] = f"API Connection Error: {conn_err}"
                    logger.error(f"Connection Error for row {i+1}: {conn_err}")
                except requests.exceptions.Timeout as timeout_err:
                    current_output_data['API_Status'] = f"API Timeout Error: {timeout_err}"
                    logger.error(f"Timeout Error for row {i+1}: {timeout_err}")
                except requests.exceptions.RequestException as req_err:
                    current_output_data['API_Status'] = f"API Request Error: {req_err}"
                    logger.error(f"Request Error for row {i+1}: {req_err}")
                except json.JSONDecodeError as json_err:
                    current_output_data['API_Status'] = f"API JSON Decode Error: {json_err} - Response: {response.text[:200]}"
                    logger.error(f"JSON Decode Error for row {i+1}: {json_err}")
                except ValueError as val_err:
                    current_output_data['API_Status'] = f"API Response Validation Error: {val_err}"
                    logger.error(f"Response Validation Error for row {i+1}: {val_err}")
                except Exception as e:
                    current_output_data['API_Status'] = f"Unexpected Script Error: {e}"
                    logger.error(f"Unexpected Error for row {i+1}: {e}")
                
                processed_rows_data.append(current_output_data)

    except FileNotFoundError:
        logger.error(f"Input CSV file '{INPUT_CSV_FILENAME}' not found.")
        return
    except Exception as e:
        logger.error(f"Failed to read or process input CSV: {e}")
        return

    if processed_rows_data:
        try:
            with open(OUTPUT_CSV_FILENAME, 'w', newline='', encoding='utf-8') as outfile:
                writer = csv.DictWriter(outfile, fieldnames=output_header)
                writer.writeheader()
                writer.writerows(processed_rows_data)
            logger.info(f"API call logging complete. Results saved to: {OUTPUT_CSV_FILENAME}")
        except Exception as e:
            logger.error(f"Failed to write output CSV: {e}")
    else:
        logger.warning("No data was processed to write to output.")

if __name__ == "__main__":
    logger.info(f"Starting API Call Logging Script...")
    logger.info(f"Input file: {INPUT_CSV_FILENAME}")
    logger.info(f"API Endpoint: {API_ENDPOINT_URL}")
    logger.info(f"Output will be saved to: {OUTPUT_CSV_FILENAME}")
    logger.info(f"Processing only first {MAX_ROWS_TO_PROCESS} rows for testing")

    if "localhost" in API_ENDPOINT_URL:
        logger.info("Running in local development mode")
    else:
        logger.info("Running in production mode")
        
    log_api_calls()
