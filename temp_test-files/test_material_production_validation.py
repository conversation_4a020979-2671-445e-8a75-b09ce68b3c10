#!/usr/bin/env python3
"""
Material Production EF Matching Validation

This script tests the 9-phase AI system against real-world material production cases
to validate improvements in geographic optimization, production method selection,
and material grade specificity.

Based on materials tab from spreadsheet showing Current EF vs Ideal EF matches.
Designed to run inside Docker container: docker exec ml-models-app python test_material_production_validation.py
"""

import sys
sys.path.append('/home/<USER>/app')

__import__('pysqlite3')
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')

import json
from typing import Dict, List, Any
from dataclasses import dataclass
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MaterialTestCase:
    """Test case representing a real-world material production scenario"""
    name: str
    material_description: str
    current_ef_match: str
    ideal_ef_match: str
    test_query: str
    expected_improvements: List[str]
    test_focus: str  # Geographic, Production Method, Material Grade, etc.
    notes: str = ""

# Material production test cases from spreadsheet
MATERIAL_TEST_CASES = [
    MaterialTestCase(
        name="Aluminum A360 Non-Recycled",
        material_description="A360 Aluminum (non-recycled)",
        current_ef_match="aluminum production, primary, ingot (customized for GLO)",
        ideal_ef_match="aluminum production, primary, ingot (RoW)",
        test_query="aluminum A360 alloy primary ingot production",
        expected_improvements=["Geographic optimization", "Regional specificity"],
        test_focus="Geographic Optimization",
        notes="Should optimize from GLO to RoW geography for better regional accuracy"
    ),

    MaterialTestCase(
        name="Steel 1.4301 Carbon Steel",
        material_description="Metal Steel (1.4301 Carbon Steel)",
        current_ef_match="steel production, electric, low-alloyed (customized for GLO)",
        ideal_ef_match="steel production, converter, low-alloyed (RoW)",
        test_query="steel 1.4301 carbon steel production low-alloyed",
        expected_improvements=["Production method optimization", "Electric to converter"],
        test_focus="Production Method Selection",
        notes="Should prefer converter production over electric for this steel grade"
    ),

    MaterialTestCase(
        name="Aluminum A380 Recycled",
        material_description="A380 Aluminum (recycled)",
        current_ef_match="treatment of aluminum scrap, post-consumer, prepared for recycling, at refiner (customized for GLO)",
        ideal_ef_match="treatment of aluminum scrap, new, at refiner (RoW)",
        test_query="aluminum A380 recycled scrap treatment refiner",
        expected_improvements=["Recycling process specificity", "Geographic optimization"],
        test_focus="Recycling vs Virgin Material",
        notes="Should distinguish recycled aluminum treatment from primary production"
    ),

    MaterialTestCase(
        name="PET Polyethylene Terephthalate",
        material_description="Polymeric Material Polyethylene Terephthalate (PET)",
        current_ef_match="polyethylene terephthalate production, granulate, amorphous (customized for GLO)",
        ideal_ef_match="polyethylene terephthalate, sheets to generic market for PET, granulate, amorphous (RER)",
        test_query="PET polyethylene terephthalate granulate amorphous production",
        expected_improvements=["Form specification", "Geographic optimization"],
        test_focus="Material Form/State Recognition",
        notes="Should recognize granulate form and optimize geography GLO to RER"
    ),

    MaterialTestCase(
        name="Nylon PA6 Production",
        material_description="Polymeric Material Nylon (PA6)",
        current_ef_match="nylon 6 production (customized for GLO)",
        ideal_ef_match="nylon 6 production",
        test_query="nylon PA6 polymer production",
        expected_improvements=["Material grade specificity", "Geographic optimization"],
        test_focus="Polymer Grade Specificity",
        notes="Should maintain PA6 specificity and optimize geography"
    ),

    MaterialTestCase(
        name="Bronze Production",
        material_description="Metal Bronze (non-recycled)",
        current_ef_match="bronze production (customized for GLO)",
        ideal_ef_match="bronze production (customized for GLO)",
        test_query="bronze metal alloy production",
        expected_improvements=["Material specificity", "Alloy composition"],
        test_focus="Metal Alloy Recognition",
        notes="Should maintain bronze alloy specificity vs generic copper/tin"
    )
]

class MaterialValidationTester:
    """Tester for validating material production EF matching improvements"""

    def __init__(self):
        self.results = []

    def test_case(self, test_case: MaterialTestCase) -> Dict[str, Any]:
        """Test a single material production case through direct function calls"""
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing: {test_case.name}")
        logger.info(f"Focus: {test_case.test_focus}")
        logger.info(f"Query: {test_case.test_query}")
        logger.info(f"Current EF: {test_case.current_ef_match}")
        logger.info(f"Ideal EF: {test_case.ideal_ef_match}")

        try:
            # Import the functions we need to test the 9-phase pipeline
            from emissions_factor_matching.predictions import (
                predict_enhanced_input_category,
                spot_modifiers,
                map_isic_classification,
                augment_query_text,
                construct_dynamic_filters,
                re_rank_candidates
            )
            from emissions_factor_matching.dataset import search_candidates_with_fallback
            from emissions_factor_matching.api import get_activity_from_dataset
            from pydantic import BaseModel

            # Create a request model that matches the expected interface
            class TestRequest(BaseModel):
                user_query_primary: str
                user_query_secondary: str | None = None
                lca_lifecycle_stage: str | None = None
                iso_code: str | None = None
                product_category_context: str | None = None
                carbon_only: bool = True  # Test with DEFRA sources preferred

            request = TestRequest(user_query_primary=test_case.test_query)

            # Run the 9-phase pipeline
            logger.info("Running 9-phase AI pipeline for material production...")

            # Phase 1.1: Enhanced Input Category Prediction
            enhanced_category = predict_enhanced_input_category(request)
            logger.info(f"Phase 1.1 - Enhanced category: {enhanced_category}")

            # Phase 1.2: Modifier Spotting
            modifiers = spot_modifiers(request, enhanced_category)
            logger.info(f"Phase 1.2 - Modifiers: {modifiers}")

            # Phase 1.3: ISIC Classification Mapping
            isic_codes = map_isic_classification(enhanced_category, modifiers, request.user_query_primary)
            logger.info(f"Phase 1.3 - ISIC codes: {isic_codes}")

            # Phase 1.4: Query Text Augmentation
            augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
            logger.info(f"Phase 1.4 - Augmented query: '{augmented_query[:100]}...'")

            # Phase 1.5: Dynamic Filter Construction
            dynamic_filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
            logger.info(f"Phase 1.5 - Dynamic filters: {dynamic_filters}")

            # Phase 1.6: ChromaDB Vector Search
            candidates = search_candidates_with_fallback(
                augmented_query=augmented_query,
                filters=dynamic_filters,
                n_results=15  # Slightly more for material production
            )
            logger.info(f"Phase 1.6 - Found {len(candidates)} candidates")

            if not candidates:
                return self._create_error_result(test_case, "No candidates found in vector search")

            # Phase 1.7: LLM Re-ranking & Justification
            matched_ef = re_rank_candidates(
                request_model=request,
                candidates=candidates,
                augmented_query=augmented_query,
                enhanced_category=enhanced_category,
                modifiers=modifiers,
                isic_codes=isic_codes
            )
            logger.info(f"Phase 1.7 - Selected: '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")

            # Phase 1.8: Geography Matching & Record Retrieval
            matched_activity = get_activity_from_dataset(
                activity_name=matched_ef.activity_name,
                iso_code="GLO",  # Default geography
                similarity=matched_ef.confidence_score or 0.0,
                reference_product_name=matched_ef.reference_product_name,
                preferred_source=matched_ef.source  # Preserve the source selected by AI pipeline
            )
            logger.info(f"Phase 1.8 - Geography matched to '{matched_activity.geography}' with source '{matched_activity.source}'")

            # Create result data structure similar to API response
            result_data = {
                "matched_activity": {
                    "activity_name": matched_activity.activity_name,
                    "source": matched_activity.source,
                    "geography": matched_activity.geography,
                    "reference_product_name": matched_activity.reference_product_name
                },
                "confidence": matched_ef.confidence,
                "explanation": matched_ef.explanation
            }

            # Analyze the result
            analysis = self._analyze_result(test_case, result_data["matched_activity"], result_data)

            logger.info(f"New System Result: {result_data['matched_activity']['activity_name']}")
            logger.info(f"Confidence: {result_data['confidence']}")
            logger.info(f"Source: {result_data['matched_activity']['source']}")
            logger.info(f"Geography: {result_data['matched_activity']['geography']}")
            logger.info(f"Analysis: {analysis['improvement_assessment']}")

            return analysis

        except Exception as e:
            logger.error(f"Error testing {test_case.name}: {str(e)}")
            import traceback
            traceback.print_exc()
            return self._create_error_result(test_case, str(e))

    def _analyze_result(self, test_case: MaterialTestCase, matched_activity: Dict, full_result: Dict) -> Dict[str, Any]:
        """Analyze how the new result compares to current and ideal EF matches for materials"""

        new_activity_name = matched_activity["activity_name"]
        new_source = matched_activity["source"]
        new_geography = matched_activity["geography"]
        confidence = full_result["confidence"]
        explanation = full_result["explanation"]

        # Material-specific improvement indicators
        improvement_indicators = []

        # Check geographic optimization
        ideal_geography = self._extract_geography(test_case.ideal_ef_match)
        current_geography = self._extract_geography(test_case.current_ef_match)

        if ideal_geography and new_geography:
            if ideal_geography.upper() == new_geography.upper():
                improvement_indicators.append("Geographic optimization achieved")
            elif ideal_geography.upper() != current_geography.upper():
                improvement_indicators.append("Geographic change detected")

        # Check production method improvements
        production_methods = ["converter", "electric", "primary", "secondary", "recycled", "virgin"]
        ideal_methods = [method for method in production_methods if method in test_case.ideal_ef_match.lower()]
        new_methods = [method for method in production_methods if method in new_activity_name.lower()]

        if ideal_methods and new_methods:
            if any(method in new_methods for method in ideal_methods):
                improvement_indicators.append("Production method alignment")

        # Check material grade/alloy specificity
        material_grades = ["A360", "A380", "1.4301", "PA6", "PA6.6", "PET"]
        query_grades = [grade for grade in material_grades if grade in test_case.test_query]

        if query_grades and any(grade in new_activity_name for grade in query_grades):
            improvement_indicators.append("Material grade specificity maintained")

        # Check recycling vs virgin distinction
        if "recycl" in test_case.test_query.lower():
            if "recycl" in new_activity_name.lower() or "scrap" in new_activity_name.lower():
                improvement_indicators.append("Recycling process recognition")

        # Check form/state recognition (granulate, sheets, pellets, etc.)
        forms = ["granulate", "sheet", "pellet", "ingot", "slab", "powder"]
        query_forms = [form for form in forms if form in test_case.test_query.lower()]
        result_forms = [form for form in forms if form in new_activity_name.lower()]

        if query_forms and any(form in result_forms for form in query_forms):
            improvement_indicators.append("Material form/state recognition")

        # Check source consistency (should be DEFRA for carbon_only=True)
        if new_source == "DEFRA":
            improvement_indicators.append("Correct source filtering (DEFRA for carbon_only)")

        # Keyword overlap analysis
        ideal_keywords = self._extract_keywords(test_case.ideal_ef_match)
        current_keywords = self._extract_keywords(test_case.current_ef_match)
        new_keywords = self._extract_keywords(new_activity_name)

        ideal_overlap = len(set(ideal_keywords) & set(new_keywords))
        current_overlap = len(set(current_keywords) & set(new_keywords))

        # Overall assessment based on test focus
        if test_case.test_focus == "Geographic Optimization":
            if "Geographic optimization achieved" in improvement_indicators:
                improvement_assessment = "SIGNIFICANT_IMPROVEMENT"
            elif "Geographic change detected" in improvement_indicators:
                improvement_assessment = "MODERATE_IMPROVEMENT"
            else:
                improvement_assessment = "NEEDS_ANALYSIS"
        elif test_case.test_focus == "Production Method Selection":
            if "Production method alignment" in improvement_indicators:
                improvement_assessment = "SIGNIFICANT_IMPROVEMENT"
            else:
                improvement_assessment = "NEEDS_ANALYSIS"
        elif test_case.test_focus == "Recycling vs Virgin Material":
            if "Recycling process recognition" in improvement_indicators:
                improvement_assessment = "SIGNIFICANT_IMPROVEMENT"
            else:
                improvement_assessment = "NEEDS_ANALYSIS"
        else:
            # General assessment
            if len(improvement_indicators) >= 3:
                improvement_assessment = "SIGNIFICANT_IMPROVEMENT"
            elif len(improvement_indicators) >= 1:
                improvement_assessment = "MODERATE_IMPROVEMENT"
            else:
                improvement_assessment = "NEEDS_ANALYSIS"

        return {
            "test_case_name": test_case.name,
            "test_focus": test_case.test_focus,
            "new_activity_name": new_activity_name,
            "new_source": new_source,
            "new_geography": new_geography,
            "confidence": confidence,
            "explanation": explanation,
            "improvement_indicators": improvement_indicators,
            "improvement_assessment": improvement_assessment,
            "ideal_keyword_overlap": ideal_overlap,
            "current_keyword_overlap": current_overlap,
            "success": True
        }

    def _extract_geography(self, text: str) -> str:
        """Extract geography codes from EF activity names"""
        geographies = ["GLO", "RoW", "RER", "US", "EU", "CN", "IN"]
        for geo in geographies:
            if f"({geo})" in text or f" {geo}" in text:
                return geo
        return ""

    def _extract_keywords(self, text: str) -> List[str]:
        """Extract meaningful keywords from EF activity names"""
        # Simple keyword extraction - can be enhanced
        stop_words = {"for", "to", "from", "of", "in", "at", "by", "with", "and", "or", "the", "a", "an", "customized"}
        words = text.lower().replace(",", " ").replace("(", " ").replace(")", " ").split()
        return [word for word in words if word not in stop_words and len(word) > 2]

    def _create_error_result(self, test_case: MaterialTestCase, error_msg: str) -> Dict[str, Any]:
        """Create error result structure"""
        return {
            "test_case_name": test_case.name,
            "test_focus": test_case.test_focus,
            "error": error_msg,
            "success": False
        }

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all material production test cases and generate summary report"""
        logger.info("🚀 Starting Material Production EF Matching Validation")
        logger.info(f"Testing {len(MATERIAL_TEST_CASES)} material production scenarios")

        results = []
        for test_case in MATERIAL_TEST_CASES:
            result = self.test_case(test_case)
            results.append(result)
            self.results.append(result)

        # Generate summary report
        summary = self._generate_summary_report(results)

        logger.info(f"\n{'='*60}")
        logger.info("📊 MATERIAL PRODUCTION VALIDATION SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Total Test Cases: {summary['total_cases']}")
        logger.info(f"Successful Tests: {summary['successful_tests']}")
        logger.info(f"Failed Tests: {summary['failed_tests']}")
        logger.info(f"Significant Improvements: {summary['significant_improvements']}")
        logger.info(f"Moderate Improvements: {summary['moderate_improvements']}")
        logger.info(f"Overall Success Rate: {summary['success_rate']:.1%}")

        # Test focus breakdown
        logger.info(f"\nTest Focus Breakdown:")
        for focus, count in summary['test_focus_breakdown'].items():
            logger.info(f"  {focus}: {count} cases")

        return summary

    def _generate_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics from test results"""
        total_cases = len(results)
        successful_tests = len([r for r in results if r.get("success", False)])
        failed_tests = total_cases - successful_tests

        successful_results = [r for r in results if r.get("success", False)]
        significant_improvements = len([r for r in successful_results if r.get("improvement_assessment") == "SIGNIFICANT_IMPROVEMENT"])
        moderate_improvements = len([r for r in successful_results if r.get("improvement_assessment") == "MODERATE_IMPROVEMENT"])

        # Test focus breakdown
        test_focus_breakdown = {}
        for result in results:
            focus = result.get("test_focus", "Unknown")
            test_focus_breakdown[focus] = test_focus_breakdown.get(focus, 0) + 1

        return {
            "total_cases": total_cases,
            "successful_tests": successful_tests,
            "failed_tests": failed_tests,
            "significant_improvements": significant_improvements,
            "moderate_improvements": moderate_improvements,
            "success_rate": successful_tests / total_cases if total_cases > 0 else 0,
            "test_focus_breakdown": test_focus_breakdown,
            "detailed_results": results
        }

def main():
    """Main execution function"""
    logger.info("🧪 Material Production EF Matching Validation")
    logger.info("Testing geographic optimization, production methods, and material specificity")
    logger.info("Running inside Docker container with direct function calls")

    tester = MaterialValidationTester()
    summary = tester.run_all_tests()

    # Save detailed results to file
    results_file = "/home/<USER>/app/material_validation_results.json"
    with open(results_file, "w") as f:
        json.dump(summary, f, indent=2)

    logger.info(f"\n✅ Material validation complete! Detailed results saved to {results_file}")

    return summary

if __name__ == "__main__":
    print("Testing Material Production EF Matching with 9-Phase AI System\n")
    main()
